import type { NextApiRequest, NextApiResponse } from 'next'
import https from 'https'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { path = [] } = req.query
  const targetUrl = `https://innovationlabwebbackend.onrender.com/${Array.isArray(path) ? path.join('/') : path}`

  const proxyReq = https.request(
    targetUrl,
    {
      method: req.method,
      headers: {
        ...req.headers,
        host: new URL(targetUrl).host,
      },
    },
    proxyRes => {
      res.statusCode = proxyRes.statusCode || 500
      proxyRes.pipe(res, { end: true })
    }
  )

  req.pipe(proxyReq, { end: true })

  proxyReq.on('error', err => {
    console.error('Proxy error:', err)
    res.status(500).json({ error: 'Proxy request failed' })
  })
}