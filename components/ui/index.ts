// Main Components
export { default as Typography } from './Typography';
export type { TypographyProps, TypographyVariant, TypographyColor, TypographyWeight, TypographyAlign } from './Typography';

export { default as Button } from './Button';
export type { ButtonProps, ButtonVariant, ButtonSize } from './Button';

export { default as MagneticButton } from './MagneticButton';
export type { MagneticButtonProps } from './MagneticButton';

export { default as Card, CardHeader, CardContent, CardFooter } from './Card';
export type { CardProps } from './Card';

export { default as Input } from './Input';
export type { InputProps } from './Input';

export { default as Badge } from './Badge';
export type { BadgeProps } from './Badge';

export { default as Modal, ModalHeader, ModalBody, ModalFooter } from './Modal';
export type { ModalProps } from './Modal';

export { default as Tooltip } from './Tooltip';
export type { TooltipProps } from './Tooltip';

export { default as Switch } from './Switch';
export type { SwitchProps } from './Switch';

export { default as Checkbox } from './Checkbox';
export type { CheckboxProps } from './Checkbox';

export { default as Radio, RadioGroup } from './Radio';
export type { RadioProps, RadioGroupProps } from './Radio';

// New Components
export { default as Select } from './Select';
export type { SelectProps, SelectOption } from './Select';

export { default as Progress } from './Progress';
export type { ProgressProps } from './Progress';

export { default as Spinner } from './Spinner';
export type { SpinnerProps } from './Spinner';

export { default as Alert } from './Alert';
export type { AlertProps } from './Alert';

export { default as Accordion, AccordionItem, AccordionHeader, AccordionContent } from './Accordion';
export type { AccordionProps, AccordionItemProps, AccordionHeaderProps, AccordionContentProps } from './Accordion';

export { default as Tabs, TabList, Tab, TabPanels, TabPanel } from './Tabs';
export type { TabsProps, TabListProps, TabProps, TabPanelsProps, TabPanelProps } from './Tabs';

export { default as Avatar } from './Avatar';
export type { AvatarProps } from './Avatar';

export { default as Breadcrumb } from './Breadcrumb';
export type { BreadcrumbProps, BreadcrumbItem } from './Breadcrumb';
