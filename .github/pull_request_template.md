## Description

Briefly describe what this PR does — e.g., new feature, UI enhancement, bug fix, or refactor.

---

## What’s Included

- [x] [Short bullet point of implemented UI/component/task]
- [ ] [Optional: Mention pending/incomplete parts, if any]

---

## Related Issues / Tasks

- Closes #[issue-number] _(if resolved by this PR)_
- Part of #[issue-number] _(if work is ongoing)_

---

## Checklist

- [x] Follows component and file naming conventions
- [x] UI matches design (Figma or mockup)
- [x] Responsive across breakpoints
- [x] Tested functionality in browser (Chrome, Firefox, etc.)
- [ ] No console warnings or errors
- [ ] State and props handled cleanly
- [ ] Loading/error states implemented
- [ ] Added/updated relevant unit/component tests
- [ ] Peer-reviewed by at least one team member

---

## Key Changes

- [Example: Created `BannerCarousel` component]
- [Example: Updated global `theme.css` with spacing tokens]
- [Briefly summarize important structural or logic changes]

---

## Notes

> Add caveats, edge cases, design questions, or TODOs for follow-up here.

---

## How to Test

1. Start the frontend dev server: `npm run dev`
2. Navigate to: `http://localhost:3000/<page>`
3. Verify the following:
   - [ ] UI renders correctly
   - [ ] Interactions (clicks, hovers, modals) work as expected
   - [ ] Data fetched/mocked displays properly
   - [ ] Responsive layout (desktop/tablet/mobile)
